import 'package:flutter_test/flutter_test.dart';
import 'package:tenanta/config/environment_config.dart';

void main() {
  group('EnvironmentConfig', () {
    test('should initialize and load environment variables', () async {
      // Initialize the environment config
      await EnvironmentConfig.initialize();
      
      // Check if configuration is loaded
      expect(EnvironmentConfig.isConfigured, isTrue);
      
      // Check if Supabase URL is loaded
      expect(EnvironmentConfig.supabaseUrl, isNotEmpty);
      expect(EnvironmentConfig.supabaseUrl, startsWith('https://'));
      
      // Check if Supabase anon key is loaded
      expect(EnvironmentConfig.supabaseAnonKey, isNotEmpty);
    });

    test('should throw exception when required variables are missing', () async {
      // This test would need a separate .env file without required variables
      // For now, we'll just test that the getters work with valid data
      await EnvironmentConfig.initialize();
      
      expect(() => EnvironmentConfig.supabaseUrl, returnsNormally);
      expect(() => EnvironmentConfig.supabase<PERSON><PERSON><PERSON>ey, returnsNormally);
    });
  });
}
